package main

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"aiops/pkg/pipeline"
	pb "aiops/pkg/proto"

	_ "github.com/go-sql-driver/mysql"
)

// MySQLCollectorPlugin MySQL 采集器插件
type MySQLCollectorPlugin struct {
	name    string
	version string
	config  map[string]interface{}
	running bool

	// MySQL连接配置
	host     string
	port     int
	username string
	password string
	database string

	// 数据库连接
	db *sql.DB

	// 统计信息
	collectCount int64
	errorCount   int64
	lastError    error
	lastCollect  time.Time
	interval     time.Duration
}

// MySQLPluginFactory MySQL插件工厂
type MySQLPluginFactory struct{}

// NewPluginFactory 插件工厂入口函数
func NewPluginFactory() pipeline.PluginFactory {
	return &MySQLPluginFactory{}
}

// CreatePlugin 创建插件实例
func (f *MySQLPluginFactory) CreatePlugin(pluginType pipeline.PluginType, config map[string]interface{}) (pipeline.PipelinePlugin, error) {
	if pluginType != pipeline.CollectorType {
		return nil, ErrUnsupportedPluginType
	}

	plugin := &MySQLCollectorPlugin{
		name:     "mysql_collector",
		version:  "1.0.0",
		config:   config,
		interval: 30 * time.Second,
	}

	return plugin, nil
}

// GetSupportedTypes 获取支持的插件类型
func (f *MySQLPluginFactory) GetSupportedTypes() []pipeline.PluginType {
	return []pipeline.PluginType{pipeline.CollectorType}
}

// GetPluginInfo 获取插件信息
func (f *MySQLPluginFactory) GetPluginInfo() *pipeline.PluginInfo {
	return &pipeline.PluginInfo{
		Name:     "mysql_collector",
		Version:  "1.0.0",
		Type:     pipeline.CollectorType,
		LoadedAt: time.Now(),
		Metadata: map[string]any{
			"description": "MySQL database monitoring collector",
			"author":      "DevInsight Team",
			"category":    "database",
		},
	}
}

// ValidateConfig 验证配置
func (f *MySQLPluginFactory) ValidateConfig(config map[string]interface{}) error {
	// 检查必需的配置项
	requiredFields := []string{"host", "port", "username", "password"}
	for _, field := range requiredFields {
		if _, exists := config[field]; !exists {
			return fmt.Errorf("missing required field: %s", field)
		}
	}
	return nil
}

// ==================== 插件实现 ====================

// GetName 获取插件名称
func (p *MySQLCollectorPlugin) GetName() string {
	return p.name
}

// GetType 获取插件类型
func (p *MySQLCollectorPlugin) GetType() pipeline.PluginType {
	return pipeline.CollectorType
}

// GetVersion 获取插件版本
func (p *MySQLCollectorPlugin) GetVersion() string {
	return p.version
}

// Initialize 初始化插件
func (p *MySQLCollectorPlugin) Initialize(config map[string]interface{}) error {
	p.config = config

	// 解析MySQL连接配置
	if host, ok := config["host"]; ok {
		if hostStr, ok := host.(string); ok {
			p.host = hostStr
		}
	}

	if port, ok := config["port"]; ok {
		switch v := port.(type) {
		case int:
			p.port = v
		case float64:
			p.port = int(v)
		case string:
			if portInt, err := strconv.Atoi(v); err == nil {
				p.port = portInt
			}
		}
	}

	if username, ok := config["username"]; ok {
		if usernameStr, ok := username.(string); ok {
			p.username = usernameStr
		}
	}

	if password, ok := config["password"]; ok {
		if passwordStr, ok := password.(string); ok {
			p.password = passwordStr
		}
	}

	if database, ok := config["database"]; ok {
		if databaseStr, ok := database.(string); ok {
			p.database = databaseStr
		}
	} else {
		p.database = "information_schema" // 默认数据库
	}

	if interval, ok := config["interval"]; ok {
		if intervalStr, ok := interval.(string); ok {
			if d, err := time.ParseDuration(intervalStr); err == nil {
				p.interval = d
			}
		}
	}

	return nil
}

// Start 启动插件
func (p *MySQLCollectorPlugin) Start(ctx context.Context) error {
	// 建立数据库连接
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s", p.username, p.password, p.host, p.port, p.database)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("failed to open MySQL connection: %w", err)
	}

	// 测试连接
	if err := db.PingContext(ctx); err != nil {
		db.Close()
		return fmt.Errorf("failed to ping MySQL server: %w", err)
	}

	p.db = db
	p.running = true

	return nil
}

// Stop 停止插件
func (p *MySQLCollectorPlugin) Stop() error {
	p.running = false

	if p.db != nil {
		p.db.Close()
		p.db = nil
	}

	return nil
}

// Health 健康检查
func (p *MySQLCollectorPlugin) Health() error {
	if !p.running {
		return ErrPluginNotRunning
	}

	if p.db == nil {
		return fmt.Errorf("database connection not established")
	}

	// 简单的ping检查
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return p.db.PingContext(ctx)
}

// Process 处理数据（采集器不需要实现）
func (p *MySQLCollectorPlugin) Process(ctx context.Context, data *pipeline.PipelineData) (*pipeline.PipelineData, error) {
	return data, nil
}

// GetConfig 获取配置
func (p *MySQLCollectorPlugin) GetConfig() map[string]interface{} {
	return p.config
}

// UpdateConfig 更新配置
func (p *MySQLCollectorPlugin) UpdateConfig(config map[string]interface{}) error {
	p.config = config
	return p.Initialize(config)
}

// GetInputSchema 获取输入模式
func (p *MySQLCollectorPlugin) GetInputSchema() *pipeline.Schema {
	return nil // 采集器没有输入
}

// GetOutputSchema 获取输出模式
func (p *MySQLCollectorPlugin) GetOutputSchema() *pipeline.Schema {
	return &pipeline.Schema{
		Fields: map[string]pipeline.FieldSchema{
			"connections": {
				Type:        "int",
				Description: "当前连接数",
				Required:    true,
			},
			"queries": {
				Type:        "int",
				Description: "查询总数",
				Required:    true,
			},
			"uptime": {
				Type:        "int",
				Description: "服务器运行时间（秒）",
				Required:    true,
			},
		},
		Description: "MySQL数据库监控数据",
		Version:     "1.0",
	}
}

// GetMetrics 获取插件指标
func (p *MySQLCollectorPlugin) GetMetrics() *pipeline.PluginMetrics {
	status := "stopped"
	if p.running {
		status = "running"
	}

	return &pipeline.PluginMetrics{
		Name:            p.name,
		ProcessedCount:  p.collectCount,
		ErrorCount:      p.errorCount,
		AvgLatency:      0,
		LastProcessTime: p.lastCollect,
		Status:          status,
		CustomMetrics: map[string]interface{}{
			"interval":   p.interval.String(),
			"host":       p.host,
			"port":       p.port,
			"database":   p.database,
			"last_error": p.getLastErrorString(),
		},
	}
}

// getLastErrorString 获取最后错误的字符串表示
func (p *MySQLCollectorPlugin) getLastErrorString() string {
	if p.lastError != nil {
		return p.lastError.Error()
	}
	return ""
}

// ==================== 采集器专用方法 ====================

// Collect 采集数据
func (p *MySQLCollectorPlugin) Collect(ctx context.Context) (*pipeline.PipelineData, error) {
	if !p.running {
		return nil, ErrPluginNotRunning
	}

	// 更新统计信息
	defer func() {
		p.collectCount++
		p.lastCollect = time.Now()
	}()

	// 采集MySQL状态信息
	metrics, err := p.collectMySQLMetrics(ctx)
	if err != nil {
		p.errorCount++
		p.lastError = err
		return nil, err
	}

	// 创建流水线数据
	data := &pipeline.PipelineData{
		ID:        generateDataID(),
		Type:      pipeline.MetricDataType,
		Source:    p.name,
		DeviceID:  fmt.Sprintf("%s:%d", p.host, p.port),
		Timestamp: time.Now(),
		Metadata: map[string]interface{}{
			"collector": p.name,
			"version":   p.version,
		},
		Context: map[string]interface{}{
			"host":     p.host,
			"port":     p.port,
			"database": p.database,
		},
		Tags: map[string]string{
			"type":   "mysql",
			"source": "database",
		},
		Metrics: metrics,
	}

	// 清除上次错误
	p.lastError = nil
	return data, nil
}

// collectMySQLMetrics 采集MySQL指标
func (p *MySQLCollectorPlugin) collectMySQLMetrics(ctx context.Context) ([]*pb.MetricData, error) {
	var metrics []*pb.MetricData
	now := time.Now().Unix()
	deviceID := fmt.Sprintf("%s:%d", p.host, p.port)

	// 查询基本状态信息
	rows, err := p.db.QueryContext(ctx, "SHOW STATUS WHERE Variable_name IN ('Threads_connected', 'Questions', 'Uptime')")
	if err != nil {
		return nil, fmt.Errorf("failed to query MySQL status: %w", err)
	}
	defer rows.Close()

	statusMap := make(map[string]string)
	for rows.Next() {
		var name, value string
		if err := rows.Scan(&name, &value); err != nil {
			continue
		}
		statusMap[name] = value
	}

	// 转换为指标数据
	if value, exists := statusMap["Threads_connected"]; exists {
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			metrics = append(metrics, &pb.MetricData{
				MetricKey: "mysql.connections.current",
				ValueType: &pb.MetricData_NumericValue{
					NumericValue: floatValue,
				},
				Timestamp: now,
				DeviceId:  deviceID,
				Labels: map[string]string{
					"type": "connection",
				},
			})
		}
	}

	if value, exists := statusMap["Questions"]; exists {
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			metrics = append(metrics, &pb.MetricData{
				MetricKey: "mysql.queries.total",
				ValueType: &pb.MetricData_NumericValue{
					NumericValue: floatValue,
				},
				Timestamp: now,
				DeviceId:  deviceID,
				Labels: map[string]string{
					"type": "query",
				},
			})
		}
	}

	if value, exists := statusMap["Uptime"]; exists {
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			metrics = append(metrics, &pb.MetricData{
				MetricKey: "mysql.uptime.seconds",
				ValueType: &pb.MetricData_NumericValue{
					NumericValue: floatValue,
				},
				Timestamp: now,
				DeviceId:  deviceID,
				Labels: map[string]string{
					"type": "uptime",
					"unit": "seconds",
				},
			})
		}
	}

	return metrics, nil
}

// GetCollectInterval 获取采集间隔
func (p *MySQLCollectorPlugin) GetCollectInterval() time.Duration {
	return p.interval
}

// SetCollectInterval 设置采集间隔
func (p *MySQLCollectorPlugin) SetCollectInterval(interval time.Duration) error {
	p.interval = interval
	return nil
}

// generateDataID 生成数据ID
func generateDataID() string {
	return fmt.Sprintf("mysql_%d", time.Now().UnixNano())
}

// 定义错误类型
var (
	ErrUnsupportedPluginType = fmt.Errorf("unsupported plugin type")
	ErrPluginNotRunning      = fmt.Errorf("plugin not running")
)

// main 函数（插件编译时需要）
func main() {
	// 插件作为共享库时不需要main函数
	// 但为了编译通过，保留空的main函数
}
