package main

import (
	"context"
	"log"
)

// Version 插件版本
var Version = "unknown"

// Plugin 插件接口实现
type Plugin struct {
	config Config
	helper Helper
}

// GetMetadata 返回插件元数据
func (p *Plugin) GetMetadata() map[string]interface{} {
	return map[string]interface{}{
		"name":        "multi_file_example",
		"version":     Version,
		"type":        "collector",
		"description": "多文件插件示例",
	}
}

// Initialize 初始化插件
func (p *Plugin) Initialize(config map[string]interface{}) error {
	p.config = NewConfig(config)
	p.helper = NewHelper()
	log.Printf("多文件插件初始化完成，版本: %s", Version)
	return nil
}

// Execute 执行插件逻辑
func (p *Plugin) Execute(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	data := p.helper.ProcessData(input)
	result := p.config.FormatOutput(data)

	return map[string]interface{}{
		"status": "success",
		"data":   result,
		"plugin": "multi_file_example",
	}, nil
}

// Cleanup 清理资源
func (p *Plugin) Cleanup() error {
	log.Println("多文件插件清理完成")
	return nil
}

// NewPlugin 导出的插件工厂函数
func NewPlugin() *Plugin {
	return &Plugin{}
}

// 导出插件实例
var PluginInstance = NewPlugin()
