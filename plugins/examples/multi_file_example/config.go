package main

import "fmt"

// Config 插件配置结构
type Config struct {
	Name     string
	Enabled  bool
	Settings map[string]interface{}
}

// NewConfig 创建新的配置实例
func NewConfig(configData map[string]interface{}) Config {
	config := Config{
		Name:     "multi_file_example",
		Enabled:  true,
		Settings: make(map[string]interface{}),
	}

	if name, ok := configData["name"].(string); ok {
		config.Name = name
	}

	if enabled, ok := configData["enabled"].(bool); ok {
		config.Enabled = enabled
	}

	if settings, ok := configData["settings"].(map[string]interface{}); ok {
		config.Settings = settings
	}

	return config
}

// FormatOutput 格式化输出数据
func (c *Config) FormatOutput(data map[string]interface{}) map[string]interface{} {
	return map[string]interface{}{
		"config_name": c.Name,
		"enabled":     c.Enabled,
		"processed":   data,
		"timestamp":   fmt.Sprintf("%d", getCurrentTimestamp()),
	}
}

func getCurrentTimestamp() int64 {
	// 简单的时间戳生成
	return 1234567890
}
