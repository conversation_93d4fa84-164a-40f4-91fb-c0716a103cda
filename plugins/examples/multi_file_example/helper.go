package main

import (
	"fmt"
	"strings"
)

// Helper 辅助工具结构
type Helper struct {
	processingCount int
}

// NewHelper 创建新的辅助工具实例
func NewHelper() Helper {
	return Helper{
		processingCount: 0,
	}
}

// ProcessData 处理输入数据
func (h *Helper) ProcessData(input map[string]interface{}) map[string]interface{} {
	h.processingCount++

	result := make(map[string]interface{})

	// 处理输入数据
	for key, value := range input {
		processedKey := h.formatKey(key)
		processedValue := h.formatValue(value)
		result[processedKey] = processedValue
	}

	result["processing_count"] = h.processingCount
	result["helper_status"] = "active"

	return result
}

// formatKey 格式化键名
func (h *Helper) formatKey(key string) string {
	return fmt.Sprintf("processed_%s", strings.ToLower(key))
}

// formatValue 格式化值
func (h *Helper) formatValue(value interface{}) interface{} {
	switch v := value.(type) {
	case string:
		return fmt.Sprintf("helper_processed: %s", v)
	case int:
		return v * 2
	case float64:
		return v * 1.5
	default:
		return fmt.Sprintf("unknown_type: %v", v)
	}
}

// GetProcessingCount 获取处理次数
func (h *Helper) GetProcessingCount() int {
	return h.processingCount
}
