2025-05-27 09:38:33.753380000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:54	Initializing plugin manager	{"plugins_dir": "../../../plugins/build"}
2025-05-27 09:38:33.805906000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/email_alerter-1.0.0.so"}
2025-05-27 09:38:34.067399000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:152	Plugin loaded successfully	{"name": "email_alerter", "version": "1.0.0", "type": "alerter"}
2025-05-27 09:38:34.067557000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/email_alerter.so"}
2025-05-27 09:38:34.067700000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82	Failed to load plugin	{"path": "../../../plugins/build/email_alerter.so", "error": "plugin already loaded: email_alerter:1.0.0"}
aiops/agent/internal/pipeline.(*PluginManager).Initialize.(*PluginManager).scanAndLoadPlugins.func1
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:345
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:369
path/filepath.Walk
	/usr/local/go/src/path/filepath/path.go:427
aiops/agent/internal/pipeline.(*PluginManager).scanAndLoadPlugins
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:74
aiops/agent/internal/pipeline.(*PluginManager).Initialize
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:62
aiops/agent/internal/pipeline.TestPluginManagerBasic
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/pipeline_test.go:19
testing.tRunner
	/usr/local/go/src/testing/testing.go:1792
2025-05-27 09:38:34.067766000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/mysql_collector-1.0.0.so"}
2025-05-27 09:38:34.283279000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:152	Plugin loaded successfully	{"name": "mysql_collector", "version": "1.0.0", "type": "collector"}
2025-05-27 09:38:34.283422000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/mysql_collector.so"}
2025-05-27 09:38:34.283577000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82	Failed to load plugin	{"path": "../../../plugins/build/mysql_collector.so", "error": "plugin already loaded: mysql_collector:1.0.0"}
aiops/agent/internal/pipeline.(*PluginManager).Initialize.(*PluginManager).scanAndLoadPlugins.func1
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:345
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:369
path/filepath.Walk
	/usr/local/go/src/path/filepath/path.go:427
aiops/agent/internal/pipeline.(*PluginManager).scanAndLoadPlugins
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:74
aiops/agent/internal/pipeline.(*PluginManager).Initialize
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:62
aiops/agent/internal/pipeline.TestPluginManagerBasic
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/pipeline_test.go:19
testing.tRunner
	/usr/local/go/src/testing/testing.go:1792
2025-05-27 09:38:34.283626000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/system_collector-1.0.0.so"}
2025-05-27 09:38:34.452533000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:152	Plugin loaded successfully	{"name": "system_collector", "version": "1.0.0", "type": "collector"}
2025-05-27 09:38:34.452626000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/system_collector.so"}
2025-05-27 09:38:34.452784000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82	Failed to load plugin	{"path": "../../../plugins/build/system_collector.so", "error": "plugin already loaded: system_collector:1.0.0"}
aiops/agent/internal/pipeline.(*PluginManager).Initialize.(*PluginManager).scanAndLoadPlugins.func1
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:345
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:369
path/filepath.Walk
	/usr/local/go/src/path/filepath/path.go:427
aiops/agent/internal/pipeline.(*PluginManager).scanAndLoadPlugins
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:74
aiops/agent/internal/pipeline.(*PluginManager).Initialize
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:62
aiops/agent/internal/pipeline.TestPluginManagerBasic
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/pipeline_test.go:19
testing.tRunner
	/usr/local/go/src/testing/testing.go:1792
2025-05-27 09:38:34.452829000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/threshold_processor-1.0.0.so"}
2025-05-27 09:38:34.651310000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:152	Plugin loaded successfully	{"name": "threshold_processor", "version": "1.0.0", "type": "processor"}
2025-05-27 09:38:34.651482000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/threshold_processor.so"}
2025-05-27 09:38:34.651672000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82	Failed to load plugin	{"path": "../../../plugins/build/threshold_processor.so", "error": "plugin already loaded: threshold_processor:1.0.0"}
aiops/agent/internal/pipeline.(*PluginManager).Initialize.(*PluginManager).scanAndLoadPlugins.func1
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:345
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:369
path/filepath.Walk
	/usr/local/go/src/path/filepath/path.go:427
aiops/agent/internal/pipeline.(*PluginManager).scanAndLoadPlugins
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:74
aiops/agent/internal/pipeline.(*PluginManager).Initialize
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:62
aiops/agent/internal/pipeline.TestPluginManagerBasic
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/pipeline_test.go:19
testing.tRunner
	/usr/local/go/src/testing/testing.go:1792
2025-05-27 09:38:34.651709000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:66	Plugin manager initialized successfully	{"loaded_plugins": 4}
2025-05-27 09:38:49.412502000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:54	Initializing plugin manager	{"plugins_dir": "../../../plugins/build"}
2025-05-27 09:38:49.419634000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/email_alerter-1.0.0.so"}
2025-05-27 09:38:49.632341000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:152	Plugin loaded successfully	{"name": "email_alerter", "version": "1.0.0", "type": "alerter"}
2025-05-27 09:38:49.632489000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/email_alerter.so"}
2025-05-27 09:38:49.632750000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82	Failed to load plugin	{"path": "../../../plugins/build/email_alerter.so", "error": "plugin already loaded: email_alerter:1.0.0"}
aiops/agent/internal/pipeline.(*PluginManager).Initialize.(*PluginManager).scanAndLoadPlugins.func1
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:345
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:369
path/filepath.Walk
	/usr/local/go/src/path/filepath/path.go:427
aiops/agent/internal/pipeline.(*PluginManager).scanAndLoadPlugins
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:74
aiops/agent/internal/pipeline.(*PluginManager).Initialize
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:62
aiops/agent/internal/pipeline.TestPluginManagerBasic
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/pipeline_test.go:19
testing.tRunner
	/usr/local/go/src/testing/testing.go:1792
2025-05-27 09:38:49.633033000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/mysql_collector-1.0.0.so"}
2025-05-27 09:38:49.853290000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:152	Plugin loaded successfully	{"name": "mysql_collector", "version": "1.0.0", "type": "collector"}
2025-05-27 09:38:49.853357000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/mysql_collector.so"}
2025-05-27 09:38:49.853444000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82	Failed to load plugin	{"path": "../../../plugins/build/mysql_collector.so", "error": "plugin already loaded: mysql_collector:1.0.0"}
aiops/agent/internal/pipeline.(*PluginManager).Initialize.(*PluginManager).scanAndLoadPlugins.func1
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:345
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:369
path/filepath.Walk
	/usr/local/go/src/path/filepath/path.go:427
aiops/agent/internal/pipeline.(*PluginManager).scanAndLoadPlugins
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:74
aiops/agent/internal/pipeline.(*PluginManager).Initialize
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:62
aiops/agent/internal/pipeline.TestPluginManagerBasic
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/pipeline_test.go:19
testing.tRunner
	/usr/local/go/src/testing/testing.go:1792
2025-05-27 09:38:49.853472000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/system_collector-1.0.0.so"}
2025-05-27 09:38:49.995432000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:152	Plugin loaded successfully	{"name": "system_collector", "version": "1.0.0", "type": "collector"}
2025-05-27 09:38:49.995533000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/system_collector.so"}
2025-05-27 09:38:49.995628000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82	Failed to load plugin	{"path": "../../../plugins/build/system_collector.so", "error": "plugin already loaded: system_collector:1.0.0"}
aiops/agent/internal/pipeline.(*PluginManager).Initialize.(*PluginManager).scanAndLoadPlugins.func1
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:345
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:369
path/filepath.Walk
	/usr/local/go/src/path/filepath/path.go:427
aiops/agent/internal/pipeline.(*PluginManager).scanAndLoadPlugins
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:74
aiops/agent/internal/pipeline.(*PluginManager).Initialize
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:62
aiops/agent/internal/pipeline.TestPluginManagerBasic
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/pipeline_test.go:19
testing.tRunner
	/usr/local/go/src/testing/testing.go:1792
2025-05-27 09:38:49.995671000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/threshold_processor-1.0.0.so"}
2025-05-27 09:38:50.176691000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:152	Plugin loaded successfully	{"name": "threshold_processor", "version": "1.0.0", "type": "processor"}
2025-05-27 09:38:50.176776000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:95	Loading plugin	{"path": "../../../plugins/build/threshold_processor.so"}
2025-05-27 09:38:50.176868000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82	Failed to load plugin	{"path": "../../../plugins/build/threshold_processor.so", "error": "plugin already loaded: threshold_processor:1.0.0"}
aiops/agent/internal/pipeline.(*PluginManager).Initialize.(*PluginManager).scanAndLoadPlugins.func1
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:82
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:345
path/filepath.walk
	/usr/local/go/src/path/filepath/path.go:369
path/filepath.Walk
	/usr/local/go/src/path/filepath/path.go:427
aiops/agent/internal/pipeline.(*PluginManager).scanAndLoadPlugins
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:74
aiops/agent/internal/pipeline.(*PluginManager).Initialize
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:62
aiops/agent/internal/pipeline.TestPluginManagerBasic
	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/pipeline_test.go:19
testing.tRunner
	/usr/local/go/src/testing/testing.go:1792
2025-05-27 09:38:50.176888000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/pipeline/plugin_manager.go:66	Plugin manager initialized successfully	{"loaded_plugins": 4}
